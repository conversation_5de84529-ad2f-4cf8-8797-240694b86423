'use client'

import SmartTriggers from "@/plugins/smart-trigger/index";
import TinyBird from "@/plugins/analytics/index"
// import {cleanupPlugins, initializePlugins} from "@/lib/plugin-lifecycle";
// import SmartTriggersFeature from "@/components/smart-trigger-plugin";

import React from "react";
import {StageTrigger} from "@/plugins/smart-trigger/types";

export type PluginConfig = {
    id : string,
    name : string,
    description : string,
    component: React.ComponentType,
    // settingsComponent: React.ComponentType,
    activate?: (context) => Promise<void>,
    deactivate?: (context) => void,
    defaultConfig?: any,
};

export type pluginState = {
    enabled: boolean;
    config: PluginConfig;
    orgConfig: any;
};

export const pluginRegistry = new Map<string, pluginState>();

export const registerPlugin = (plugin: PluginConfig) => {
  if (pluginRegistry.has(plugin.id)) {
    throw new Error(`Plugin with id "${plugin.id}" is already registered.`);
  };

  pluginRegistry.set(plugin.id, {
    config: plugin,
    enabled: false,
    orgConfig: plugin.defaultConfig || {},
  });
};

export const getPlugins = () => Array.from(pluginRegistry.entries()).map(([id, state]) => ({
  id,
  ...state,
}));

// Fetch plugins from backend
export const fetchPlugins = async (orgId?: string) => {
  const response = await fetch(`/api/plugins?orgId=${orgId}`);
  const plugins = await response.json();

  for (const plugin of plugins.enabled) {
    const existing = pluginRegistry.get(plugin);
    if (existing) {
      existing.enabled = true;
      pluginRegistry.set(plugin, existing);
    }
  };
  return getPlugins();
};

export const togglePlugin = async (pluginId: string, enabled: boolean, orgId?: string) => {
  const plugin = pluginRegistry.get(pluginId);

  if (plugin) {
    plugin.enabled = enabled;
    pluginRegistry.set(pluginId, plugin);
    await fetch(`/api/plugins?orgId=${orgId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ enabled, pluginId }),
    });
  };
};

export const isPluginActive = (pluginId: string) => {
  const plugin = pluginRegistry.get(pluginId);
  return plugin?.enabled || false;
};
