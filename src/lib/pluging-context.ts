"use client";

import { addTrigger } from "@/server/queries";
import {StageTrigger} from "@/plugins/smart-trigger/types";

class PluginContext {
    private static instance: PluginContext;
    private jobId: string | null = null;
    private triggers: StageTrigger[] = [];

    setJobId(jobId: string) {
        this.jobId = jobId;
    };

    getJobId() {
        return this.jobId;
    };

    setTriggers(triggers: StageTrigger[]) {
        this.triggers = triggers;
    };

    public static getInstance(): PluginContext {
      if (!PluginContext.instance) {
        PluginContext.instance = new PluginContext();
      }
      return PluginContext.instance;
    };

    onTriggerActivated(applicationId: number, stageId: number, stageName: string) {
        console.log("Trigger activated");
        const stageTriggers = this.triggers.filter((t) => Number(t.id) === stageId);
        stageTriggers.forEach((trigger) => {
            trigger.actions.forEach(async (action) => {
                if (action.action_type === null) return;
                await addTrigger(applicationId, action, stageName);
            });
        });
    };
};

export const usePluginContext = PluginContext.getInstance();
