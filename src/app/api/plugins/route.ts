import {NextRequest, NextResponse} from "next/server";
import {db} from "@/drizzle/db";
import {eq} from "drizzle-orm";
import {organization} from "@/drizzle/schema";

// export async function POST(req: NextRequest){
//     const searchParams = req.nextUrl.searchParams;
//     const orgId = searchParams.get('orgId');
//
//      if (!orgId) {
//         console.error("orgId is missing from the query parameters.");
//         return NextResponse.json({error: "orgId is required"}, {status: 400});
//     }
//
//     // await db.insert()
// }

export async function GET(req: NextRequest) {
    const searchParams = req.nextUrl.searchParams;
    const orgId = searchParams.get('orgId');

    if (!orgId) {
        console.error("orgId is missing from the query parameters.");
        return NextResponse.json({error: "orgId is required"}, {status: 400});
    }
    // You can now use orgId to fetch data or perform other operations
    // For example, you might query a database or perform some logic here
    const [organi] = await db.select().from(organization).where(eq(organization.clerk_id, orgId));
    return NextResponse.json(organi.plugins || {enabled: [], settings: {}});
};

export async function POST(req: NextRequest) {
    const searchParams = req.nextUrl.searchParams;
    const orgId = searchParams.get('orgId');
    const {enabled, pluginId} = await req.json();

    if (!orgId) {
        console.error("orgId is missing from the query parameters.");
        return NextResponse.json({error: "orgId is required"}, {status: 400});
    }

    console.log("orgId", orgId);

  try {
    const result = await db
      .select({ plugins: organization.plugins })
      .from(organization)
      .where(eq(organization.clerk_id, orgId));

    if (!result.length) {
      return NextResponse.json({ error: 'Organization not found' });
    };
    console.log("result", result);

    const pluginsData = result[0].plugins as { enabled: string[]; settings?: object };
    const updatedEnabled = enabled
      ? [...new Set([...pluginsData.enabled, pluginId])] // Add pluginId
      : pluginsData.enabled.filter((id: string) => id !== pluginId); // Remove pluginId

    const updatedPlugins = {
      ...pluginsData,
      enabled: updatedEnabled,
    };

    await db
      .update(organization)
      .set({ plugins: updatedPlugins })
      .where(eq(organization.clerk_id, orgId));


    return NextResponse.json({message: "Success", enabledPlugins: updatedEnabled });
  } catch (error) {
    console.error('Error toggling plugin:', error);
    return NextResponse.json({error: "Internal server error"}, {status: 500});
  }

};
