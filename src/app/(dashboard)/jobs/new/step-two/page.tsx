'use client';

import {Input} from "@/components/ui/input";
import {FormErrors} from "@/types";
import CustomButton from "@/components/custom-button";
import React, {useActionState, useEffect, useState} from "react";
import {stepTwoFormAction} from "@/app/(dashboard)/jobs/new/step-two/_actions";
import MultiSelect from "@/components/multi-select";
import {techSchema} from "@/zod";
import {FormControl, FormItem, FormLabel, FormMessage} from "@/components/ui/form";
import {useForm} from "react-hook-form";
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {Button} from "@/components/ui/button";
import {z} from "zod";
import {Plus, X, Code, Award} from "lucide-react";
import StepOneCollapse from "@/app/(dashboard)/jobs/new/_component/step-one-collapse";
import {useNewJobContext} from "@/providers/new-job-provider";
import {<PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle} from "@/components/ui/card";
import {Badge} from "@/components/ui/badge";

const initialState: FormErrors = {};

export default function StepTwoForm() {
    const [currentStages, setCurrentStages] = useState<z.infer<typeof techSchema>[]>([]);
    const [serverErrors, formAction] = useActionState(stepTwoFormAction, initialState);
    const form = useForm();
    const {updateNewJobDetails, newJobData, removeJob} = useNewJobContext();

    useEffect(() => {
        setCurrentStages(newJobData.jobTechnology);
    }, [newJobData]);

    return (
        <div className="space-y-8">
            {/* Header */}
            <div className="text-center space-y-2">
                <h1 className="text-3xl font-bold text-gray-900">Requirements</h1>
                <p className="text-gray-600">Define the skills and experience needed for this role</p>
            </div>

            {/* Job Summary */}
            <StepOneCollapse />

            <form action={formData => {
                formData.append("jobTechnology", JSON.stringify(currentStages))
                formAction(formData)
            }} className="space-y-8">

                {/* Requirements Card */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Code size={20} className="text-blue-500" />
                            Technical Requirements
                        </CardTitle>
                        <p className="text-sm text-gray-600">Add the technologies and experience levels required</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Current Requirements */}
                        {currentStages.length > 0 && (
                            <div className="space-y-3">
                                <h3 className="font-medium text-gray-900">Added Requirements</h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {currentStages.map((item, index) => (
                                        <div key={index} className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                            <div className="flex items-center gap-3">
                                                <Award size={16} className="text-blue-600" />
                                                <div>
                                                    <p className="font-medium text-gray-900">{item.technology}</p>
                                                    <p className="text-sm text-gray-600">{item.year_of_experience} years experience</p>
                                                </div>
                                            </div>
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeJob(item, "jobTechnology")}
                                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                            >
                                                <X size={16} />
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Add New Requirement */}
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                            <MultiSelect
                                className="w-full"
                                schema={techSchema}
                                fieldName={"jobTechnology"}
                                setValue={form.setValue}
                                getValues={form.getValues}
                                renderForm={(onSubmit, forms) => (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" className="w-full h-12 border-dashed">
                                                <Plus size={18} className="mr-2" />
                                                Add Technical Requirement
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className="w-80 p-4">
                                            <div className="space-y-4">
                                                <FormItem>
                                                    <FormLabel>Technology/Skill</FormLabel>
                                                    <FormControl>
                                                        <Input 
                                                            {...forms.register("technology")}
                                                            placeholder="e.g. React, Python, AWS"
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>

                                                <FormItem>
                                                    <FormLabel>Years of Experience</FormLabel>
                                                    <FormControl>
                                                        <Input 
                                                            {...forms.register("year_of_experience")}
                                                            type="number"
                                                            placeholder="e.g. 3"
                                                        />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>

                                                <Button 
                                                    type="button"
                                                    onClick={() => {
                                                        form.setValue('jobTechnology', currentStages);
                                                        onSubmit(forms.watch() as z.infer<typeof techSchema>)
                                                        updateNewJobDetails(forms.watch() as z.infer<typeof techSchema>, "jobTechnology")
                                                    }}
                                                    className="w-full"
                                                >
                                                    Add Requirement
                                                </Button>
                                            </div>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                )}
                            />
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex justify-between pt-6 border-t">
                    <Button variant="outline" type="button" onClick={() => window.history.back()}>
                        Back
                    </Button>
                    <CustomButton 
                        text="Continue to Workflow" 
                        className="px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                    />
                </div>
            </form>
        </div>
    );
}