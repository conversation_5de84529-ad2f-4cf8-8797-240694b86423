import ModernDashboard from "@/app/(dashboard)/dashboard/_components/modern-dashboard";
import DashboardLayout from "@/app/(dashboard)/dashboard/_components/dashboard-layout";
import { auth, currentUser } from "@clerk/nextjs/server";
import { getRangeOption, RANGE_OPTIONS } from "@/lib/utils";
import {
    getDashboardMetrics,
    getRecentActivity,
    getUpcomingInterviews,
    getJobPipelineData
} from "@/server/actions/dashboard-actions";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

type Props = {
    searchParams: {
        [key: string]: string | undefined;
    };
};

const Page = async ({ searchParams }: Props) => {
    const { range, rangeFrom, rangeTo, view } = searchParams ?? {};

    const user = await currentUser();
    const { orgId } = await auth();

    if (!orgId) return null;

    const chartRange =
        getRangeOption(range, rangeFrom, rangeTo) || RANGE_OPTIONS.last_7_days;

    // Fetch dashboard data
    const [metrics, recentActivity, upcomingInterviews, jobPipeline] = await Promise.all([
        getDashboardMetrics(),
        getRecentActivity(),
        getUpcomingInterviews(),
        getJobPipelineData()
    ]);

    const currentView = view || 'modern';

    return (
        <div className="min-h-screen bg-gray-50/50">
            <Tabs value={currentView} className="w-full">
                <div className="border-b bg-white px-6 py-4">
                    <TabsList className="grid w-fit grid-cols-2">
                        <TabsTrigger value="modern">Modern View</TabsTrigger>
                        <TabsTrigger value="classic">Classic View</TabsTrigger>
                    </TabsList>
                </div>

                <TabsContent value="modern" className="mt-0">
                    <ModernDashboard
                        metrics={metrics}
                        recentActivity={recentActivity}
                        upcomingInterviews={upcomingInterviews}
                        jobPipeline={jobPipeline}
                        userName={user?.fullName || 'User'}
                    />
                </TabsContent>

                <TabsContent value="classic" className="mt-0">
                    <div className="p-4 lg:max-w-8xl 2xl:mx-auto">
                        <div className="h-[150px] mt-2 rounded grid md:grid-cols-6 gap-8">
                            <div className="md:col-span-4 flex flex-col justify-between gap-4 h-full flex-1">
                                <div className="mt-2">
                                    <h2 className="text-xl font-semibold text-gray-900">
                                        Welcome, {user?.fullName}
                                    </h2>
                                    <p className="text-sm text-gray-500">
                                        View and manage your organization&apos;s dashboard
                                    </p>
                                </div>
                            </div>
                        </div>

                        <DashboardLayout chartRange={chartRange} />
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default Page;
