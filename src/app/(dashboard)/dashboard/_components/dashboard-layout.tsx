'use client'

import React, { useEffect } from 'react';
import {ChartInterface} from "@/app/(dashboard)/dashboard/_components/charts/radar-chart";
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from "@/components/ui/card";
import Link from "next/link";
import ChartCard from "@/app/(dashboard)/dashboard/_components/chart-card";
import StatCard from '@/components/stat-card';
import pluginConfig from "@/plugins/smart-trigger";
import {usePluginContextHook} from "@/providers/plugins-provider";
import { useSearchParams } from 'next/navigation';
import { getRangeOption, RANGE_OPTIONS } from '@/lib/utils';
import { pluginRegistry } from '@/lib/plugins-registry';

type Props = {
    chartRange: {startDate: Date | null, endDate: Date | null};
    // job_open: ChartInterface[] | null;
    // hired_candidates: ChartInterface[] | null;
    // job_listings: ChartInterface[] | null;
    // applications: ChartInterface[] | null;
};

const DashboardLayout = ({chartRange}: Props) => {
    const context = usePluginContextHook();
    const analyticsPlugin = pluginRegistry.get("TinyBird");
    const {job, hired, open_job, applications} = context.analytics;

    useEffect(() => {
        if (analyticsPlugin?.config && typeof analyticsPlugin.config.actions?.getChartData === "function" && analyticsPlugin.enabled) {
            analyticsPlugin.config.actions.getChartData(context, chartRange.startDate, chartRange.endDate);
        }
    }, [chartRange.startDate, chartRange.endDate]);

    return (
        <div className="">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
                <ChartCard
                    queryKey="range"
                    job_open={open_job as ChartInterface[]}
                    hired_candidates={hired as ChartInterface[]}
                    job_listings={job as ChartInterface[]}
                />

                <Card className="md:col-span-1 row-span-2 bg-white border border-transparent rounded">
                    <CardHeader>
                        <CardTitle>Activities</CardTitle>
                        <CardDescription>
                            Showing total visitors for the last 6 months
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <pluginConfig.component />
                    </CardContent>
                </Card>

                <div className="md:col-span-2">
                    <div className="flex items-center justify-between">
                        <h3 className="font-semibold leading-none tracking-tight">Analytics</h3>
                        <Link className="text-sm text-blue-500" href=''>See more</Link>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
                        <StatCard title="Last applicants" end={2000} description="Last 30 days" duration={2} />
                        <StatCard title="Jobs Openings" end={2000} description='Last 30 days'duration={2}/>
                        <StatCard title="Hires" end={500} description='Last 30 days'duration={2}/>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DashboardLayout;
