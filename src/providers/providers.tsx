"use client";

import { SidebarProvider } from "@/components/ui/sidebar";
import { TriggerProvider } from "./trigger-provider";
import { NewJobContextProvider } from "./new-job-provider";
import { fetchPlugins, getPlugins, registerPlugin } from "@/lib/plugins-registry";
import { useEffect } from "react";

import SmartTriggers from "@/plugins/smart-trigger/index";
import TinyBird from "@/plugins/analytics/index"
import { usePluginContext } from "@/lib/pluging-context";

type Props = {
  children: React.ReactNode;
  orgId: string;
};

registerPlugin(SmartTriggers);
registerPlugin(TinyBird);

const Provider = ({ children, orgId }: Props) => {
    const context = usePluginContext;

    useEffect(() => {
        fetchPlugins(orgId).then(() => {
            getPlugins().forEach((plugin) => {
                if (plugin.enabled && plugin.config.activate) {
                    plugin.config.activate(context);
                    }
                });
            });
    }, []);

  return (
    <TriggerProvider>
      <NewJobContextProvider>
        <SidebarProvider
          style={
            {
              "--sidebar-width": "350px",
            } as React.CSSProperties
          }
        >
          {children}
        </SidebarProvider>
      </NewJobContextProvider>
    </TriggerProvider>
  );
};

export default Provider;
