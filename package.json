{"name": "koze-ats", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "email": "email dev --dir src/emails"}, "dependencies": {"@clerk/nextjs": "^6.9.6", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "0.1.1", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-table": "^8.20.6", "bullmq": "^5.41.8", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.44.2", "formidable": "^3.5.2", "googleapis": "^144.0.0", "ioredis": "^5.6.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.469.0", "mongoose": "^8.12.1", "motion": "^11.15.0", "mysql2": "^3.11.5", "next": "15.1.2", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.1", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-xarrows": "^2.0.2", "recharts": "^2.15.4", "resend": "^4.6.0", "svix": "^1.44.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/formidable": "^3.4.5", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/pdf-parse": "^1.1.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.1.2", "postcss": "^8", "react-email": "4.0.17", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5"}}